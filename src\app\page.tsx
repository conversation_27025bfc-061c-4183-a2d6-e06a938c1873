'use client';

import { useState, useEffect } from 'react';
import { NewsArticle, NewsCategory } from '@/lib/types';
import { fetchNews, searchNews } from '@/lib/newsApi';
import { NewsCard } from '@/components/NewsCard';
import { SearchBar } from '@/components/SearchBar';
import { CategoryFilter } from '@/components/CategoryFilter';
import { NewsGridSkeleton } from '@/components/NewsCardSkeleton';
import { Button } from '@/components/ui/button';
import { RefreshCw, Newspaper } from 'lucide-react';

export default function Home() {
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<NewsCategory | 'general'>('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const loadNews = async (category?: NewsCategory | 'general', query?: string) => {
    setLoading(true);
    try {
      let response;
      if (query) {
        response = await searchNews(query);
      } else {
        response = await fetchNews({
          category: category === 'general' ? undefined : category
        });
      }
      setArticles(response.articles);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching news:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNews(selectedCategory);
  }, [selectedCategory]);

  const handleCategoryChange = (category: NewsCategory | 'general') => {
    setSelectedCategory(category);
    setSearchQuery('');
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setSelectedCategory('general');
    loadNews('general', query);
  };

  const handleRefresh = () => {
    if (searchQuery) {
      loadNews('general', searchQuery);
    } else {
      loadNews(selectedCategory);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Newspaper className="h-8 w-8 text-primary" />
              <h1 className="text-2xl font-bold">NewsAug</h1>
            </div>
            <div className="flex items-center gap-4">
              <SearchBar onSearch={handleSearch} />
              <Button
                variant="outline"
                size="icon"
                onClick={handleRefresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          <CategoryFilter
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
          />
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold">
              {searchQuery ? `Search results for "${searchQuery}"` :
               selectedCategory === 'general' ? 'Latest News' :
               `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} News`}
            </h2>
            <p className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          </div>
          {!loading && (
            <p className="text-sm text-muted-foreground">
              {articles.length} article{articles.length !== 1 ? 's' : ''} found
            </p>
          )}
        </div>

        {loading ? (
          <NewsGridSkeleton />
        ) : articles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {articles.map((article) => (
              <NewsCard key={article.id} article={article} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Newspaper className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No articles found</h3>
            <p className="text-muted-foreground">
              {searchQuery
                ? 'Try searching with different keywords'
                : 'No articles available for this category'}
            </p>
          </div>
        )}
      </main>
    </div>
  );
}
