import { NewsResponse, NewsFilters, NewsArticle } from './types';

// For demo purposes, we'll use mock data since NewsAPI requires an API key
// In a real app, you would use: const API_KEY = process.env.NEXT_PUBLIC_NEWS_API_KEY;
const BASE_URL = 'https://newsapi.org/v2';

// Mock data for demonstration
const mockArticles: NewsArticle[] = [
  {
    id: '1',
    title: 'Breaking: Major Tech Company Announces Revolutionary AI Breakthrough',
    description: 'A leading technology company has unveiled a groundbreaking artificial intelligence system that promises to transform how we interact with digital devices.',
    content: 'In a landmark announcement today, the company revealed their latest AI system that demonstrates unprecedented capabilities in natural language understanding and generation...',
    url: 'https://example.com/tech-breakthrough',
    urlToImage: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop',
    publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    source: { id: 'tech-news', name: 'Tech News Daily' },
    author: '<PERSON>',
    category: 'technology'
  },
  {
    id: '2',
    title: 'Global Climate Summit Reaches Historic Agreement',
    description: 'World leaders have reached a consensus on new climate policies that could significantly impact global carbon emissions.',
    content: 'After days of intense negotiations, representatives from over 190 countries have agreed to implement new measures...',
    url: 'https://example.com/climate-summit',
    urlToImage: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?w=800&h=400&fit=crop',
    publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
    source: { id: 'global-news', name: 'Global News Network' },
    author: 'Michael Chen',
    category: 'science'
  },
  {
    id: '3',
    title: 'Stock Markets Rally as Economic Indicators Show Positive Trends',
    description: 'Major stock indices have seen significant gains following the release of encouraging economic data.',
    content: 'Markets opened higher today as investors responded positively to new economic indicators showing growth...',
    url: 'https://example.com/market-rally',
    urlToImage: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=400&fit=crop',
    publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
    source: { id: 'business-wire', name: 'Business Wire' },
    author: 'Emily Rodriguez',
    category: 'business'
  },
  {
    id: '4',
    title: 'Championship Game Delivers Thrilling Overtime Victory',
    description: 'In a nail-biting finish, the home team secured victory in overtime to claim the championship title.',
    content: 'The championship game lived up to all expectations as both teams battled through regulation time...',
    url: 'https://example.com/championship-game',
    urlToImage: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=800&h=400&fit=crop',
    publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
    source: { id: 'sports-central', name: 'Sports Central' },
    author: 'David Thompson',
    category: 'sports'
  },
  {
    id: '5',
    title: 'New Medical Research Shows Promise for Cancer Treatment',
    description: 'Scientists have published groundbreaking research that could lead to more effective cancer treatments.',
    content: 'A team of researchers has made significant progress in developing a new approach to cancer treatment...',
    url: 'https://example.com/medical-research',
    urlToImage: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=400&fit=crop',
    publishedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
    source: { id: 'health-today', name: 'Health Today' },
    author: 'Dr. Lisa Wang',
    category: 'health'
  },
  {
    id: '6',
    title: 'Entertainment Industry Embraces Virtual Reality Experiences',
    description: 'Major entertainment companies are investing heavily in virtual reality to create immersive experiences.',
    content: 'The entertainment industry is undergoing a transformation as companies explore new ways to engage audiences...',
    url: 'https://example.com/vr-entertainment',
    urlToImage: 'https://images.unsplash.com/photo-1592478411213-6153e4ebc696?w=800&h=400&fit=crop',
    publishedAt: new Date(Date.now() - 16 * 60 * 60 * 1000).toISOString(), // 16 hours ago
    source: { id: 'entertainment-weekly', name: 'Entertainment Weekly' },
    author: 'Alex Martinez',
    category: 'entertainment'
  }
];

export async function fetchNews(filters: NewsFilters = {}): Promise<NewsResponse> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  let filteredArticles = [...mockArticles];
  
  // Apply filters
  if (filters.category && filters.category !== 'general') {
    filteredArticles = filteredArticles.filter(article => article.category === filters.category);
  }
  
  if (filters.q) {
    const query = filters.q.toLowerCase();
    filteredArticles = filteredArticles.filter(article => 
      article.title.toLowerCase().includes(query) ||
      article.description.toLowerCase().includes(query)
    );
  }
  
  // Sort articles
  if (filters.sortBy === 'publishedAt') {
    filteredArticles.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());
  }
  
  return {
    status: 'ok',
    totalResults: filteredArticles.length,
    articles: filteredArticles
  };
}

export async function fetchTopHeadlines(category?: string): Promise<NewsResponse> {
  return fetchNews({ category: category as any, sortBy: 'publishedAt' });
}

export async function searchNews(query: string): Promise<NewsResponse> {
  return fetchNews({ q: query, sortBy: 'relevancy' });
}
