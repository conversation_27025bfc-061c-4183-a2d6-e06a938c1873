"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/moon.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Moon)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z\",\n            key: \"a7tn18\"\n        }\n    ]\n];\nconst Moon = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"moon\", __iconNode);\n //# sourceMappingURL=moon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbW9vbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFzQztZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDckU7QUFhTSxXQUFPLGtFQUFpQixTQUFRLENBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXHNyY1xcaWNvbnNcXG1vb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMTIgM2E2IDYgMCAwIDAgOSA5IDkgOSAwIDEgMS05LTlaJywga2V5OiAnYTd0bjE4JyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBNb29uXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVElnTTJFMklEWWdNQ0F3SURBZ09TQTVJRGtnT1NBd0lERWdNUzA1TFRsYUlpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL21vb25cbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBNb29uID0gY3JlYXRlTHVjaWRlSWNvbignbW9vbicsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBNb29uO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/sun.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Sun)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"4\",\n            key: \"4exip2\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2v2\",\n            key: \"tus03m\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 20v2\",\n            key: \"1lh1kg\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m4.93 4.93 1.41 1.41\",\n            key: \"149t6j\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m17.66 17.66 1.41 1.41\",\n            key: \"ptbguv\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 12h2\",\n            key: \"1t8f8n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 12h2\",\n            key: \"1q8mjw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6.34 17.66-1.41 1.41\",\n            key: \"1m8zz5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m19.07 4.93-1.41 1.41\",\n            key: \"1shlcs\"\n        }\n    ]\n];\nconst Sun = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"sun\", __iconNode);\n //# sourceMappingURL=sun.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_newsApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/newsApi */ \"(app-pages-browser)/./src/lib/newsApi.ts\");\n/* harmony import */ var _components_NewsCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/NewsCard */ \"(app-pages-browser)/./src/components/NewsCard.tsx\");\n/* harmony import */ var _components_SearchBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SearchBar */ \"(app-pages-browser)/./src/components/SearchBar.tsx\");\n/* harmony import */ var _components_CategoryFilter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CategoryFilter */ \"(app-pages-browser)/./src/components/CategoryFilter.tsx\");\n/* harmony import */ var _components_NewsCardSkeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NewsCardSkeleton */ \"(app-pages-browser)/./src/components/NewsCardSkeleton.tsx\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Newspaper,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Newspaper,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [articles, setArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const loadNews = async (category, query)=>{\n        setLoading(true);\n        try {\n            let response;\n            if (query) {\n                response = await (0,_lib_newsApi__WEBPACK_IMPORTED_MODULE_2__.searchNews)(query);\n            } else {\n                response = await (0,_lib_newsApi__WEBPACK_IMPORTED_MODULE_2__.fetchNews)({\n                    category: category === 'general' ? undefined : category\n                });\n            }\n            setArticles(response.articles);\n            setLastUpdated(new Date());\n        } catch (error) {\n            console.error('Error fetching news:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setMounted(true);\n            setLastUpdated(new Date());\n            loadNews(selectedCategory);\n        }\n    }[\"Home.useEffect\"], [\n        selectedCategory\n    ]);\n    // Auto-refresh every 5 minutes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    if (searchQuery) {\n                        loadNews('general', searchQuery);\n                    } else {\n                        loadNews(selectedCategory);\n                    }\n                }\n            }[\"Home.useEffect.interval\"], 5 * 60 * 1000); // 5 minutes\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        selectedCategory,\n        searchQuery\n    ]);\n    const handleCategoryChange = (category)=>{\n        setSelectedCategory(category);\n        setSearchQuery('');\n    };\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        setSelectedCategory('general');\n        loadNews('general', query);\n    };\n    const handleRefresh = ()=>{\n        if (searchQuery) {\n            loadNews('general', searchQuery);\n        } else {\n            loadNews(selectedCategory);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"NewsAug\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchBar__WEBPACK_IMPORTED_MODULE_4__.SearchBar, {\n                                            onSearch: handleSearch\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                            variant: \"outline\",\n                                            size: \"icon\",\n                                            onClick: handleRefresh,\n                                            disabled: loading,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 \".concat(loading ? 'animate-spin' : '')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_7__.ThemeToggle, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryFilter__WEBPACK_IMPORTED_MODULE_5__.CategoryFilter, {\n                            selectedCategory: selectedCategory,\n                            onCategoryChange: handleCategoryChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold\",\n                                        children: searchQuery ? 'Search results for \"'.concat(searchQuery, '\"') : selectedCategory === 'general' ? 'Latest News' : \"\".concat(selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1), \" News\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    mounted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            \"Last updated: \",\n                                            lastUpdated ? lastUpdated.toLocaleTimeString() : 'Loading...'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    articles.length,\n                                    \" article\",\n                                    articles.length !== 1 ? 's' : '',\n                                    \" found\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewsCardSkeleton__WEBPACK_IMPORTED_MODULE_6__.NewsGridSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this) : articles.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: articles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewsCard__WEBPACK_IMPORTED_MODULE_3__.NewsCard, {\n                                article: article\n                            }, article.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No articles found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: searchQuery ? 'Try searching with different keywords' : 'No articles available for this category'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Fn458U3AHVk5rwJ1AE8pOBVMmvU=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst initialState = {\n    theme: 'system',\n    setTheme: ()=>null\n};\nconst ThemeProviderContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(initialState);\nfunction ThemeProvider(param) {\n    let { children, defaultTheme = 'system', storageKey = 'newsaug-theme', ...props } = param;\n    _s();\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Only access localStorage after component mounts to avoid hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            var _localStorage;\n            setMounted(true);\n            const savedTheme = (_localStorage = localStorage) === null || _localStorage === void 0 ? void 0 : _localStorage.getItem(storageKey);\n            if (savedTheme) {\n                setTheme(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        storageKey\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const root = window.document.documentElement;\n            root.classList.remove('light', 'dark');\n            if (theme === 'system') {\n                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                root.classList.add(systemTheme);\n                return;\n            }\n            root.classList.add(theme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    const value = {\n        theme,\n        setTheme: (theme)=>{\n            if (mounted) {\n                var _localStorage;\n                (_localStorage = localStorage) === null || _localStorage === void 0 ? void 0 : _localStorage.setItem(storageKey, theme);\n            }\n            setTheme(theme);\n        }\n    };\n    // Don't render until mounted to avoid hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProviderContext.Provider, {\n        ...props,\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeProvider, \"O2iXXcoCegWUrHJV7GB0gi7uKwY=\");\n_c = ThemeProvider;\nconst useTheme = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeProviderContext);\n    if (context === undefined) throw new Error('useTheme must be used within a ThemeProvider');\n    return context;\n};\n_s1(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(app-pages-browser)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ ThemeToggle auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ThemeToggle() {\n    _s();\n    const { theme, setTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ThemeToggle.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ThemeToggle.useEffect\"], []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            variant: \"outline\",\n            size: \"icon\",\n            disabled: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-[1.2rem] w-[1.2rem]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Toggle theme\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        variant: \"outline\",\n        size: \"icon\",\n        onClick: ()=>setTheme(theme === 'light' ? 'dark' : 'light'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeToggle, \"uGU5l7ciDSfqFDe6wS7vfMb29jQ=\", false, function() {\n    return [\n        _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme\n    ];\n});\n_c = ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeToggle.tsx\n"));

/***/ })

});