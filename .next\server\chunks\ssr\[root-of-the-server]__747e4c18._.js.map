{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/lib/newsApi.ts"], "sourcesContent": ["import { NewsResponse, NewsFilters, NewsArticle } from './types';\n\n// For demo purposes, we'll use mock data since NewsAPI requires an API key\n// In a real app, you would use: const API_KEY = process.env.NEXT_PUBLIC_NEWS_API_KEY;\nconst BASE_URL = 'https://newsapi.org/v2';\n\n// Mock data for demonstration\nconst mockArticles: NewsArticle[] = [\n  {\n    id: '1',\n    title: 'Breaking: Major Tech Company Announces Revolutionary AI Breakthrough',\n    description: 'A leading technology company has unveiled a groundbreaking artificial intelligence system that promises to transform how we interact with digital devices.',\n    content: 'In a landmark announcement today, the company revealed their latest AI system that demonstrates unprecedented capabilities in natural language understanding and generation...',\n    url: 'https://example.com/tech-breakthrough',\n    urlToImage: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop',\n    publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n    source: { id: 'tech-news', name: 'Tech News Daily' },\n    author: '<PERSON>',\n    category: 'technology'\n  },\n  {\n    id: '2',\n    title: 'Global Climate Summit Reaches Historic Agreement',\n    description: 'World leaders have reached a consensus on new climate policies that could significantly impact global carbon emissions.',\n    content: 'After days of intense negotiations, representatives from over 190 countries have agreed to implement new measures...',\n    url: 'https://example.com/climate-summit',\n    urlToImage: 'https://images.unsplash.com/photo-1569163139394-de4e4f43e4e3?w=800&h=400&fit=crop',\n    publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n    source: { id: 'global-news', name: 'Global News Network' },\n    author: 'Michael Chen',\n    category: 'science'\n  },\n  {\n    id: '3',\n    title: 'Stock Markets Rally as Economic Indicators Show Positive Trends',\n    description: 'Major stock indices have seen significant gains following the release of encouraging economic data.',\n    content: 'Markets opened higher today as investors responded positively to new economic indicators showing growth...',\n    url: 'https://example.com/market-rally',\n    urlToImage: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=400&fit=crop',\n    publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n    source: { id: 'business-wire', name: 'Business Wire' },\n    author: 'Emily Rodriguez',\n    category: 'business'\n  },\n  {\n    id: '4',\n    title: 'Championship Game Delivers Thrilling Overtime Victory',\n    description: 'In a nail-biting finish, the home team secured victory in overtime to claim the championship title.',\n    content: 'The championship game lived up to all expectations as both teams battled through regulation time...',\n    url: 'https://example.com/championship-game',\n    urlToImage: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=800&h=400&fit=crop',\n    publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago\n    source: { id: 'sports-central', name: 'Sports Central' },\n    author: 'David Thompson',\n    category: 'sports'\n  },\n  {\n    id: '5',\n    title: 'New Medical Research Shows Promise for Cancer Treatment',\n    description: 'Scientists have published groundbreaking research that could lead to more effective cancer treatments.',\n    content: 'A team of researchers has made significant progress in developing a new approach to cancer treatment...',\n    url: 'https://example.com/medical-research',\n    urlToImage: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=400&fit=crop',\n    publishedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago\n    source: { id: 'health-today', name: 'Health Today' },\n    author: 'Dr. Lisa Wang',\n    category: 'health'\n  },\n  {\n    id: '6',\n    title: 'Entertainment Industry Embraces Virtual Reality Experiences',\n    description: 'Major entertainment companies are investing heavily in virtual reality to create immersive experiences.',\n    content: 'The entertainment industry is undergoing a transformation as companies explore new ways to engage audiences...',\n    url: 'https://example.com/vr-entertainment',\n    urlToImage: 'https://images.unsplash.com/photo-1592478411213-6153e4ebc696?w=800&h=400&fit=crop',\n    publishedAt: new Date(Date.now() - 16 * 60 * 60 * 1000).toISOString(), // 16 hours ago\n    source: { id: 'entertainment-weekly', name: 'Entertainment Weekly' },\n    author: 'Alex Martinez',\n    category: 'entertainment'\n  }\n];\n\nexport async function fetchNews(filters: NewsFilters = {}): Promise<NewsResponse> {\n  // Simulate API delay\n  await new Promise(resolve => setTimeout(resolve, 1000));\n  \n  let filteredArticles = [...mockArticles];\n  \n  // Apply filters\n  if (filters.category && filters.category !== 'general') {\n    filteredArticles = filteredArticles.filter(article => article.category === filters.category);\n  }\n  \n  if (filters.q) {\n    const query = filters.q.toLowerCase();\n    filteredArticles = filteredArticles.filter(article => \n      article.title.toLowerCase().includes(query) ||\n      article.description.toLowerCase().includes(query)\n    );\n  }\n  \n  // Sort articles\n  if (filters.sortBy === 'publishedAt') {\n    filteredArticles.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());\n  }\n  \n  return {\n    status: 'ok',\n    totalResults: filteredArticles.length,\n    articles: filteredArticles\n  };\n}\n\nexport async function fetchTopHeadlines(category?: string): Promise<NewsResponse> {\n  return fetchNews({ category: category as any, sortBy: 'publishedAt' });\n}\n\nexport async function searchNews(query: string): Promise<NewsResponse> {\n  return fetchNews({ q: query, sortBy: 'relevancy' });\n}\n"], "names": [], "mappings": ";;;;;AAEA,2EAA2E;AAC3E,sFAAsF;AACtF,MAAM,WAAW;AAEjB,8BAA8B;AAC9B,MAAM,eAA8B;IAClC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,KAAK;QACL,YAAY;QACZ,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QAClE,QAAQ;YAAE,IAAI;YAAa,MAAM;QAAkB;QACnD,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,KAAK;QACL,YAAY;QACZ,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QAClE,QAAQ;YAAE,IAAI;YAAe,MAAM;QAAsB;QACzD,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,KAAK;QACL,YAAY;QACZ,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QAClE,QAAQ;YAAE,IAAI;YAAiB,MAAM;QAAgB;QACrD,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,KAAK;QACL,YAAY;QACZ,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QAClE,QAAQ;YAAE,IAAI;YAAkB,MAAM;QAAiB;QACvD,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,KAAK;QACL,YAAY;QACZ,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;QACnE,QAAQ;YAAE,IAAI;YAAgB,MAAM;QAAe;QACnD,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,KAAK;QACL,YAAY;QACZ,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;QACnE,QAAQ;YAAE,IAAI;YAAwB,MAAM;QAAuB;QACnE,QAAQ;QACR,UAAU;IACZ;CACD;AAEM,eAAe,UAAU,UAAuB,CAAC,CAAC;IACvD,qBAAqB;IACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,IAAI,mBAAmB;WAAI;KAAa;IAExC,gBAAgB;IAChB,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,WAAW;QACtD,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;IAC7F;IAEA,IAAI,QAAQ,CAAC,EAAE;QACb,MAAM,QAAQ,QAAQ,CAAC,CAAC,WAAW;QACnC,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,UACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;IAE/C;IAEA,gBAAgB;IAChB,IAAI,QAAQ,MAAM,KAAK,eAAe;QACpC,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;IACrG;IAEA,OAAO;QACL,QAAQ;QACR,cAAc,iBAAiB,MAAM;QACrC,UAAU;IACZ;AACF;AAEO,eAAe,kBAAkB,QAAiB;IACvD,OAAO,UAAU;QAAE,UAAU;QAAiB,QAAQ;IAAc;AACtE;AAEO,eAAe,WAAW,KAAa;IAC5C,OAAO,UAAU;QAAE,GAAG;QAAO,QAAQ;IAAY;AACnD", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/NewsCard.tsx"], "sourcesContent": ["import { NewsArticle } from '@/lib/types';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { formatDistanceToNow } from 'date-fns';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface NewsCardProps {\n  article: NewsArticle;\n}\n\nexport function NewsCard({ article }: NewsCardProps) {\n  const timeAgo = formatDistanceToNow(new Date(article.publishedAt), { addSuffix: true });\n\n  return (\n    <Card className=\"overflow-hidden hover:shadow-lg transition-shadow duration-300\">\n      <div className=\"relative h-48 w-full\">\n        <Image\n          src={article.urlToImage || 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&h=400&fit=crop'}\n          alt={article.title}\n          fill\n          className=\"object-cover\"\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n        />\n      </div>\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <Badge variant=\"secondary\" className=\"text-xs\">\n            {article.source.name}\n          </Badge>\n          <span className=\"text-xs text-muted-foreground\">{timeAgo}</span>\n        </div>\n        <CardTitle className=\"line-clamp-2 text-lg leading-tight\">\n          <Link\n            href={article.url}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"hover:text-primary transition-colors\"\n          >\n            {article.title}\n          </Link>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-0\">\n        <CardDescription className=\"line-clamp-3 text-sm leading-relaxed\">\n          {article.description}\n        </CardDescription>\n        {article.author && (\n          <p className=\"text-xs text-muted-foreground mt-3\">\n            By {article.author}\n          </p>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAMO,SAAS,SAAS,EAAE,OAAO,EAAiB;IACjD,MAAM,UAAU,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,QAAQ,WAAW,GAAG;QAAE,WAAW;IAAK;IAErF,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK,QAAQ,UAAU,IAAI;oBAC3B,KAAK,QAAQ,KAAK;oBAClB,IAAI;oBACJ,WAAU;oBACV,OAAM;;;;;;;;;;;0BAGV,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,QAAQ,MAAM,CAAC,IAAI;;;;;;0CAEtB,8OAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;;kCAEnD,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,QAAQ,GAAG;4BACjB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAET,QAAQ,KAAK;;;;;;;;;;;;;;;;;0BAIpB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCACxB,QAAQ,WAAW;;;;;;oBAErB,QAAQ,MAAM,kBACb,8OAAC;wBAAE,WAAU;;4BAAqC;4BAC5C,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport { Search } from 'lucide-react';\n\ninterface SearchBarProps {\n  onSearch: (query: string) => void;\n  placeholder?: string;\n}\n\nexport function SearchBar({ onSearch, placeholder = \"Search news...\" }: SearchBarProps) {\n  const [query, setQuery] = useState('');\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (query.trim()) {\n      onSearch(query.trim());\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"flex gap-2 w-full max-w-md\">\n      <div className=\"relative flex-1\">\n        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\n        <Input\n          type=\"text\"\n          placeholder={placeholder}\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          className=\"pl-10\"\n        />\n      </div>\n      <Button type=\"submit\" disabled={!query.trim()}>\n        Search\n      </Button>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,UAAU,EAAE,QAAQ,EAAE,cAAc,gBAAgB,EAAkB;IACpF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,SAAS,MAAM,IAAI;QACrB;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC,iIAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,WAAU;;;;;;;;;;;;0BAGd,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,CAAC,MAAM,IAAI;0BAAI;;;;;;;;;;;;AAKrD", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/CategoryFilter.tsx"], "sourcesContent": ["'use client';\n\nimport { NewsCategory } from '@/lib/types';\nimport { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';\n\ninterface CategoryFilterProps {\n  selectedCategory: NewsCategory | 'general';\n  onCategoryChange: (category: NewsCategory | 'general') => void;\n}\n\nconst categories: { value: NewsCategory | 'general'; label: string }[] = [\n  { value: 'general', label: 'All' },\n  { value: 'technology', label: 'Technology' },\n  { value: 'business', label: 'Business' },\n  { value: 'sports', label: 'Sports' },\n  { value: 'health', label: 'Health' },\n  { value: 'science', label: 'Science' },\n  { value: 'entertainment', label: 'Entertainment' },\n];\n\nexport function CategoryFilter({ selectedCategory, onCategoryChange }: CategoryFilterProps) {\n  return (\n    <Tabs value={selectedCategory} onValueChange={(value) => onCategoryChange(value as NewsCategory | 'general')}>\n      <TabsList className=\"grid w-full grid-cols-7 lg:w-auto lg:grid-cols-7\">\n        {categories.map((category) => (\n          <TabsTrigger\n            key={category.value}\n            value={category.value}\n            className=\"text-xs sm:text-sm\"\n          >\n            {category.label}\n          </TabsTrigger>\n        ))}\n      </TabsList>\n    </Tabs>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUA,MAAM,aAAmE;IACvE;QAAE,OAAO;QAAW,OAAO;IAAM;IACjC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAiB,OAAO;IAAgB;CAClD;AAEM,SAAS,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAuB;IACxF,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,OAAO;QAAkB,eAAe,CAAC,QAAU,iBAAiB;kBACxE,cAAA,8OAAC,gIAAA,CAAA,WAAQ;YAAC,WAAU;sBACjB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,gIAAA,CAAA,cAAW;oBAEV,OAAO,SAAS,KAAK;oBACrB,WAAU;8BAET,SAAS,KAAK;mBAJV,SAAS,KAAK;;;;;;;;;;;;;;;AAU/B", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/components/NewsCardSkeleton.tsx"], "sourcesContent": ["import { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { Skeleton } from '@/components/ui/skeleton';\n\nexport function NewsCardSkeleton() {\n  return (\n    <Card className=\"overflow-hidden\">\n      <Skeleton className=\"h-48 w-full\" />\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <Skeleton className=\"h-4 w-20\" />\n          <Skeleton className=\"h-3 w-16\" />\n        </div>\n        <Skeleton className=\"h-6 w-full mb-2\" />\n        <Skeleton className=\"h-6 w-3/4\" />\n      </CardHeader>\n      <CardContent className=\"pt-0\">\n        <Skeleton className=\"h-4 w-full mb-2\" />\n        <Skeleton className=\"h-4 w-full mb-2\" />\n        <Skeleton className=\"h-4 w-2/3 mb-3\" />\n        <Skeleton className=\"h-3 w-24\" />\n      </CardContent>\n    </Card>\n  );\n}\n\nexport function NewsGridSkeleton() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {Array.from({ length: 6 }).map((_, i) => (\n        <NewsCardSkeleton key={i} />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAEtB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI5B;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,sBAAsB;;;;;;;;;;AAI/B", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/NewsAug/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { NewsArticle, NewsCategory } from '@/lib/types';\nimport { fetchNews, searchNews } from '@/lib/newsApi';\nimport { NewsCard } from '@/components/NewsCard';\nimport { SearchBar } from '@/components/SearchBar';\nimport { CategoryFilter } from '@/components/CategoryFilter';\nimport { NewsGridSkeleton } from '@/components/NewsCardSkeleton';\nimport { Button } from '@/components/ui/button';\nimport { RefreshCw, Newspaper } from 'lucide-react';\n\nexport default function Home() {\n  const [articles, setArticles] = useState<NewsArticle[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedCategory, setSelectedCategory] = useState<NewsCategory | 'general'>('general');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());\n\n  const loadNews = async (category?: NewsCategory | 'general', query?: string) => {\n    setLoading(true);\n    try {\n      let response;\n      if (query) {\n        response = await searchNews(query);\n      } else {\n        response = await fetchNews({\n          category: category === 'general' ? undefined : category\n        });\n      }\n      setArticles(response.articles);\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Error fetching news:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadNews(selectedCategory);\n  }, [selectedCategory]);\n\n  // Auto-refresh every 5 minutes\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (searchQuery) {\n        loadNews('general', searchQuery);\n      } else {\n        loadNews(selectedCategory);\n      }\n    }, 5 * 60 * 1000); // 5 minutes\n\n    return () => clearInterval(interval);\n  }, [selectedCategory, searchQuery]);\n\n  const handleCategoryChange = (category: NewsCategory | 'general') => {\n    setSelectedCategory(category);\n    setSearchQuery('');\n  };\n\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n    setSelectedCategory('general');\n    loadNews('general', query);\n  };\n\n  const handleRefresh = () => {\n    if (searchQuery) {\n      loadNews('general', searchQuery);\n    } else {\n      loadNews(selectedCategory);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center gap-2\">\n              <Newspaper className=\"h-8 w-8 text-primary\" />\n              <h1 className=\"text-2xl font-bold\">NewsAug</h1>\n            </div>\n            <div className=\"flex items-center gap-4\">\n              <SearchBar onSearch={handleSearch} />\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={handleRefresh}\n                disabled={loading}\n              >\n                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />\n              </Button>\n            </div>\n          </div>\n\n          <CategoryFilter\n            selectedCategory={selectedCategory}\n            onCategoryChange={handleCategoryChange}\n          />\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div>\n            <h2 className=\"text-xl font-semibold\">\n              {searchQuery ? `Search results for \"${searchQuery}\"` :\n               selectedCategory === 'general' ? 'Latest News' :\n               `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} News`}\n            </h2>\n            <p className=\"text-sm text-muted-foreground\">\n              Last updated: {lastUpdated.toLocaleTimeString()}\n            </p>\n          </div>\n          {!loading && (\n            <p className=\"text-sm text-muted-foreground\">\n              {articles.length} article{articles.length !== 1 ? 's' : ''} found\n            </p>\n          )}\n        </div>\n\n        {loading ? (\n          <NewsGridSkeleton />\n        ) : articles.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {articles.map((article) => (\n              <NewsCard key={article.id} article={article} />\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <Newspaper className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">No articles found</h3>\n            <p className=\"text-muted-foreground\">\n              {searchQuery\n                ? 'Try searching with different keywords'\n                : 'No articles available for this category'}\n            </p>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEzD,MAAM,WAAW,OAAO,UAAqC;QAC3D,WAAW;QACX,IAAI;YACF,IAAI;YACJ,IAAI,OAAO;gBACT,WAAW,MAAM,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE;YAC9B,OAAO;gBACL,WAAW,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;oBACzB,UAAU,aAAa,YAAY,YAAY;gBACjD;YACF;YACA,YAAY,SAAS,QAAQ;YAC7B,eAAe,IAAI;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;IACX,GAAG;QAAC;KAAiB;IAErB,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,IAAI,aAAa;gBACf,SAAS,WAAW;YACtB,OAAO;gBACL,SAAS;YACX;QACF,GAAG,IAAI,KAAK,OAAO,YAAY;QAE/B,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAkB;KAAY;IAElC,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,eAAe;IACjB;IAEA,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,oBAAoB;QACpB,SAAS,WAAW;IACtB;IAEA,MAAM,gBAAgB;QACpB,IAAI,aAAa;YACf,SAAS,WAAW;QACtB,OAAO;YACL,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;;;;;;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,+HAAA,CAAA,YAAS;4CAAC,UAAU;;;;;;sDACrB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU;sDAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;sCAKtE,8OAAC,oIAAA,CAAA,iBAAc;4BACb,kBAAkB;4BAClB,kBAAkB;;;;;;;;;;;;;;;;;0BAMxB,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,cAAc,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC,GACnD,qBAAqB,YAAY,gBACjC,GAAG,iBAAiB,MAAM,CAAC,GAAG,WAAW,KAAK,iBAAiB,KAAK,CAAC,GAAG,KAAK,CAAC;;;;;;kDAEjF,8OAAC;wCAAE,WAAU;;4CAAgC;4CAC5B,YAAY,kBAAkB;;;;;;;;;;;;;4BAGhD,CAAC,yBACA,8OAAC;gCAAE,WAAU;;oCACV,SAAS,MAAM;oCAAC;oCAAS,SAAS,MAAM,KAAK,IAAI,MAAM;oCAAG;;;;;;;;;;;;;oBAKhE,wBACC,8OAAC,sIAAA,CAAA,mBAAgB;;;;+BACf,SAAS,MAAM,GAAG,kBACpB,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,8HAAA,CAAA,WAAQ;gCAAkB,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;6CAI7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAE,WAAU;0CACV,cACG,0CACA;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}]}