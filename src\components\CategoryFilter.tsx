'use client';

import { NewsCategory } from '@/lib/types';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface CategoryFilterProps {
  selectedCategory: NewsCategory | 'general';
  onCategoryChange: (category: NewsCategory | 'general') => void;
}

const categories: { value: NewsCategory | 'general'; label: string }[] = [
  { value: 'general', label: 'All' },
  { value: 'technology', label: 'Technology' },
  { value: 'business', label: 'Business' },
  { value: 'sports', label: 'Sports' },
  { value: 'health', label: 'Health' },
  { value: 'science', label: 'Science' },
  { value: 'entertainment', label: 'Entertainment' },
];

export function CategoryFilter({ selectedCategory, onCategoryChange }: CategoryFilterProps) {
  return (
    <Tabs value={selectedCategory} onValueChange={(value) => onCategoryChange(value as NewsCategory | 'general')}>
      <TabsList className="grid w-full grid-cols-7 lg:w-auto lg:grid-cols-7">
        {categories.map((category) => (
          <TabsTrigger
            key={category.value}
            value={category.value}
            className="text-xs sm:text-sm"
          >
            {category.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
