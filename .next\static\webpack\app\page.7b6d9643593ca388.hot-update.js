"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_newsApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/newsApi */ \"(app-pages-browser)/./src/lib/newsApi.ts\");\n/* harmony import */ var _components_NewsCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/NewsCard */ \"(app-pages-browser)/./src/components/NewsCard.tsx\");\n/* harmony import */ var _components_SearchBar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SearchBar */ \"(app-pages-browser)/./src/components/SearchBar.tsx\");\n/* harmony import */ var _components_CategoryFilter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CategoryFilter */ \"(app-pages-browser)/./src/components/CategoryFilter.tsx\");\n/* harmony import */ var _components_NewsCardSkeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NewsCardSkeleton */ \"(app-pages-browser)/./src/components/NewsCardSkeleton.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Newspaper,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Newspaper,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [articles, setArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('general');\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const loadNews = async (category, query)=>{\n        setLoading(true);\n        try {\n            let response;\n            if (query) {\n                response = await (0,_lib_newsApi__WEBPACK_IMPORTED_MODULE_2__.searchNews)(query);\n            } else {\n                response = await (0,_lib_newsApi__WEBPACK_IMPORTED_MODULE_2__.fetchNews)({\n                    category: category === 'general' ? undefined : category\n                });\n            }\n            setArticles(response.articles);\n            setLastUpdated(new Date());\n        } catch (error) {\n            console.error('Error fetching news:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setMounted(true);\n            setLastUpdated(new Date());\n            loadNews(selectedCategory);\n        }\n    }[\"Home.useEffect\"], [\n        selectedCategory\n    ]);\n    // Auto-refresh every 5 minutes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const interval = setInterval({\n                \"Home.useEffect.interval\": ()=>{\n                    if (searchQuery) {\n                        loadNews('general', searchQuery);\n                    } else {\n                        loadNews(selectedCategory);\n                    }\n                }\n            }[\"Home.useEffect.interval\"], 5 * 60 * 1000); // 5 minutes\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        selectedCategory,\n        searchQuery\n    ]);\n    const handleCategoryChange = (category)=>{\n        setSelectedCategory(category);\n        setSearchQuery('');\n    };\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        setSelectedCategory('general');\n        loadNews('general', query);\n    };\n    const handleRefresh = ()=>{\n        if (searchQuery) {\n            loadNews('general', searchQuery);\n        } else {\n            loadNews(selectedCategory);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: \"NewsAug\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchBar__WEBPACK_IMPORTED_MODULE_4__.SearchBar, {\n                                            onSearch: handleSearch\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"outline\",\n                                            size: \"icon\",\n                                            onClick: handleRefresh,\n                                            disabled: loading,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 \".concat(loading ? 'animate-spin' : '')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryFilter__WEBPACK_IMPORTED_MODULE_5__.CategoryFilter, {\n                            selectedCategory: selectedCategory,\n                            onCategoryChange: handleCategoryChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold\",\n                                        children: searchQuery ? 'Search results for \"'.concat(searchQuery, '\"') : selectedCategory === 'general' ? 'Latest News' : \"\".concat(selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1), \" News\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    mounted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            \"Last updated: \",\n                                            lastUpdated ? lastUpdated.toLocaleTimeString() : 'Loading...'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    articles.length,\n                                    \" article\",\n                                    articles.length !== 1 ? 's' : '',\n                                    \" found\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewsCardSkeleton__WEBPACK_IMPORTED_MODULE_6__.NewsGridSkeleton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this) : articles.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: articles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewsCard__WEBPACK_IMPORTED_MODULE_3__.NewsCard, {\n                                article: article\n                            }, article.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Newspaper_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No articles found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: searchQuery ? 'Try searching with different keywords' : 'No articles available for this category'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\NewsAug\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"Fn458U3AHVk5rwJ1AE8pOBVMmvU=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});