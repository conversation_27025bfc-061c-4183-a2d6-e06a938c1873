# NewsAug - Modern Real-time News App

A modern, responsive real-time news application built with Next.js 15, TypeScript, and shadcn/ui components.

## Features

- 🚀 **Real-time Updates**: Auto-refreshes every 5 minutes to keep content fresh
- 🎨 **Modern UI**: Built with shadcn/ui components and Tailwind CSS
- 🌙 **Dark/Light Mode**: Toggle between themes with system preference detection
- 📱 **Responsive Design**: Works seamlessly on desktop, tablet, and mobile
- 🔍 **Search Functionality**: Search for specific news topics
- 📂 **Category Filtering**: Browse news by categories (Technology, Business, Sports, etc.)
- ⚡ **Fast Loading**: Skeleton loading states for better UX
- 🎯 **TypeScript**: Fully typed for better development experience

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Icons**: Lucide React
- **Date Handling**: date-fns
- **Font**: <PERSON>eist <PERSON> & Geist Mono

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

2. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout with theme provider
│   ├── page.tsx            # Main news page
│   └── globals.css         # Global styles
├── components/
│   ├── ui/                 # shadcn/ui components
│   ├── NewsCard.tsx        # Individual news article card
│   ├── SearchBar.tsx       # Search functionality
│   ├── CategoryFilter.tsx  # Category tabs
│   ├── NewsCardSkeleton.tsx # Loading skeletons
│   ├── ThemeProvider.tsx   # Theme context provider
│   └── ThemeToggle.tsx     # Dark/light mode toggle
└── lib/
    ├── types.ts            # TypeScript type definitions
    └── newsApi.ts          # News API service with mock data
```

## Features in Detail

### Real-time Updates
- Automatically refreshes news every 5 minutes
- Manual refresh button available
- Shows last updated timestamp

### Search & Filtering
- Real-time search across article titles and descriptions
- Category-based filtering (All, Technology, Business, Sports, Health, Science, Entertainment)
- Maintains search state across interactions

### Theme Support
- Light and dark mode support
- System preference detection
- Persistent theme selection
- Smooth transitions between themes

### Responsive Design
- Mobile-first approach
- Adaptive grid layout (1 column on mobile, 2 on tablet, 3 on desktop)
- Touch-friendly interface

## API Integration

Currently uses mock data for demonstration. To integrate with a real news API:

1. Sign up for a news API service (e.g., NewsAPI.org)
2. Add your API key to environment variables
3. Update the `newsApi.ts` file to use real API endpoints

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
