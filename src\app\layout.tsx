import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ThemeProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "NewsAug - Real-time News App",
  description: "Stay updated with the latest news from around the world. Browse by category, search for specific topics, and get real-time updates.",
  keywords: ["news", "real-time", "breaking news", "technology", "business", "sports", "health"],
  authors: [{ name: "NewsAug Team" }],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          defaultTheme="system"
          storageKey="newsaug-theme"
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
